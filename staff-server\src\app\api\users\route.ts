/**
 * Staff user management endpoints
 * Handles CRUD operations for staff users (management, reception, teacher)
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest, hashPassword } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  parsePaginationParams, 
  parseFilterParams,
  validateRequiredFields,
  isValidEmail
} from '@/lib/utils';
import { logUserOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/shared/types/common';
import { buildWhereClause } from '@/lib/db';

interface User {
  id: string;
  email: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

// GET /api/users - List staff users with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Only management can view all users
    if (authResult.user.role !== UserRole.MANAGEMENT) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    try {
      // Build WHERE clause for filtering
      const conditions: Record<string, any> = {};
      let paramIndex = 1;

      if (filters.search) {
        // Search in name and email
        const searchResult = await query<{total: string}>(
          `SELECT COUNT(*) as total FROM users
           WHERE (name ILIKE $1 OR email ILIKE $1)`,
          [`%${filters.search}%`]
        );
        const total = parseInt(searchResult.rows[0].total);

        const offset = (pagination.page - 1) * pagination.limit;
        const usersResult = await query<User>(
          `SELECT id, email, role, name, is_active, created_at, updated_at 
           FROM users 
           WHERE (name ILIKE $1 OR email ILIKE $1)
           ORDER BY created_at DESC 
           LIMIT $2 OFFSET $3`,
          [`%${filters.search}%`, pagination.limit, offset]
        );

        return createResponse({
          users: usersResult.rows,
          pagination: {
            page: pagination.page,
            limit: pagination.limit,
            total,
            totalPages: Math.ceil(total / pagination.limit),
            hasNext: pagination.page < Math.ceil(total / pagination.limit),
            hasPrev: pagination.page > 1
          }
        }, true, 'Users retrieved successfully');
      }

      // Add role filter
      if (filters.role && ['management', 'reception', 'teacher'].includes(filters.role)) {
        conditions.role = filters.role;
      }

      // Add active status filter
      if (filters.isActive !== undefined) {
        conditions.is_active = filters.isActive === 'true';
      }

      const { whereClause, params, nextIndex } = buildWhereClause(conditions, paramIndex);
      paramIndex = nextIndex;

      // Get total count
      const countSql = `SELECT COUNT(*) as total FROM users ${whereClause}`;
      const countResult = await query(countSql, params);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated results
      const offset = (pagination.page - 1) * pagination.limit;
      const dataSql = `
        SELECT id, email, role, name, is_active, created_at, updated_at 
        FROM users ${whereClause} 
        ORDER BY created_at DESC 
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const usersResult = await query<User>(dataSql, [...params, pagination.limit, offset]);

      return createResponse({
        users: usersResult.rows,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page < Math.ceil(total / pagination.limit),
          hasPrev: pagination.page > 1
        }
      }, true, 'Users retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving staff users:', dbError);
      return createErrorResponse('Failed to retrieve users', 500);
    }

  } catch (error) {
    console.error('Get staff users error:', error);
    return createErrorResponse('Failed to retrieve users', 500);
  }
}

// POST /api/users - Create new staff user
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Only management can create users
    if (authResult.user.role !== UserRole.MANAGEMENT) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { email, password, role, name, isActive = true } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['email', 'password', 'role', 'name']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    // Validate email format
    if (!isValidEmail(email)) {
      return createErrorResponse('Invalid email format', 400);
    }

    // Validate role
    if (!['management', 'reception', 'teacher'].includes(role)) {
      return createErrorResponse('Invalid role. Must be management, reception, or teacher', 400);
    }

    // Validate password strength
    if (password.length < 8) {
      return createErrorResponse('Password must be at least 8 characters long', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Check if user already exists
      const existingUserResult = await query(
        'SELECT id FROM users WHERE email = $1',
        [email.toLowerCase()]
      );

      if (existingUserResult.rows.length > 0) {
        return createErrorResponse('User with this email already exists', 409);
      }

      // Hash password
      const passwordHash = await hashPassword(password);

      // Create user
      const userResult = await query<User>(
        `INSERT INTO users (email, password_hash, role, name, is_active)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING id, email, role, name, is_active, created_at, updated_at`,
        [email.toLowerCase(), passwordHash, role, name, isActive]
      );

      const newUser = userResult.rows[0];

      // Log user creation
      await logUserOperation(
        'CREATE' as any,
        authResult.user.id,
        newUser,
        undefined,
        context
      );

      return createResponse({
        id: newUser.id,
        email: newUser.email,
        role: newUser.role,
        name: newUser.name,
        isActive: newUser.is_active,
        createdAt: newUser.created_at,
        updatedAt: newUser.updated_at
      }, true, 'User created successfully', undefined, 201);

    } catch (dbError) {
      console.error('Database error creating staff user:', dbError);
      return createErrorResponse('Failed to create user', 500);
    }

  } catch (error) {
    console.error('Create staff user error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}
