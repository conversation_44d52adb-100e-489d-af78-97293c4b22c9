# Database
DATABASE_URL=postgresql://admin_owner:<EMAIL>/admin?sslmode=require

# Authentication
JWT_SECRET=your-jwt-secret-key-admin-server
NEXTAUTH_SECRET=your-nextauth-secret-admin

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# Activity Logging
ENABLE_ACTIVITY_LOGGING=true
LOG_RETENTION_DAYS=365

# Staff Server Integration
STAFF_SERVER_API_KEY=your-staff-server-api-key
STAFF_SERVER_URL=http://localhost:3001
AUTO_CREATE_INVOICE=true
DEFAULT_MONTHLY_FEE=100

# Payment Configuration
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
