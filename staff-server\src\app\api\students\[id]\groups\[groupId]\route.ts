/**
 * Individual student-group relationship management
 * Handles updating and removing student from specific group
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse,
  isValidUUID
} from '@/lib/utils';
import { logStudentOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/shared/types/common';

interface StudentGroup {
  id: string;
  student_id: string;
  group_id: string;
  enrollment_date: Date;
  status: string;
  created_at: Date;
  updated_at: Date;
}

// PUT /api/students/[id]/groups/[groupId] - Update student-group relationship
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; groupId: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id, groupId } = await params;

    // Validate UUID formats
    if (!isValidUUID(id) || !isValidUUID(groupId)) {
      return createErrorResponse('Invalid student or group ID format', 400);
    }

    // Check permissions - management and reception can update enrollments
    if (!['management', 'reception'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { status, enrollmentDate } = body;

    // Validate status if provided
    if (status && !['active', 'inactive', 'completed'].includes(status)) {
      return createErrorResponse('Invalid status. Must be active, inactive, or completed', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Check if student-group relationship exists
      const relationshipResult = await query<StudentGroup>(
        `SELECT sg.*, s.first_name, s.last_name, g.name as group_name
         FROM student_groups sg
         JOIN students s ON sg.student_id = s.id
         JOIN groups g ON sg.group_id = g.id
         WHERE sg.student_id = $1 AND sg.group_id = $2`,
        [id, groupId]
      );

      if (relationshipResult.rows.length === 0) {
        return createErrorResponse('Student-group relationship not found', 404);
      }

      const currentRelationship = relationshipResult.rows[0];

      // Build update query
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      if (status !== undefined) {
        updateFields.push(`status = $${paramIndex}`);
        updateValues.push(status);
        paramIndex++;
      }

      if (enrollmentDate !== undefined) {
        updateFields.push(`enrollment_date = $${paramIndex}`);
        updateValues.push(enrollmentDate);
        paramIndex++;
      }

      if (updateFields.length === 0) {
        return createErrorResponse('No fields to update', 400);
      }

      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
      updateValues.push(id, groupId);

      const updateSql = `
        UPDATE student_groups 
        SET ${updateFields.join(', ')}
        WHERE student_id = $${paramIndex} AND group_id = $${paramIndex + 1}
        RETURNING id, student_id, group_id, enrollment_date, status, created_at, updated_at
      `;

      const updatedRelationshipResult = await query<StudentGroup>(updateSql, updateValues);
      const updatedRelationship = updatedRelationshipResult.rows[0];

      // Log the update
      await logStudentOperation(
        'UPDATE' as any,
        authResult.user.id,
        {
          id: currentRelationship.student_id,
          first_name: currentRelationship.first_name,
          last_name: currentRelationship.last_name,
          groupUpdate: {
            groupId: currentRelationship.group_id,
            groupName: currentRelationship.group_name,
            oldStatus: currentRelationship.status,
            newStatus: updatedRelationship.status,
            enrollmentDate: updatedRelationship.enrollment_date
          }
        },
        currentRelationship,
        context
      );

      return createResponse({
        id: updatedRelationship.id,
        studentId: updatedRelationship.student_id,
        groupId: updatedRelationship.group_id,
        groupName: currentRelationship.group_name,
        enrollmentDate: updatedRelationship.enrollment_date,
        status: updatedRelationship.status,
        updatedAt: updatedRelationship.updated_at
      }, true, 'Student-group relationship updated successfully');

    } catch (dbError) {
      console.error('Database error updating student-group relationship:', dbError);
      return createErrorResponse('Failed to update student-group relationship', 500);
    }

  } catch (error) {
    console.error('Update student-group relationship error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}

// DELETE /api/students/[id]/groups/[groupId] - Remove student from group
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; groupId: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id, groupId } = await params;

    // Validate UUID formats
    if (!isValidUUID(id) || !isValidUUID(groupId)) {
      return createErrorResponse('Invalid student or group ID format', 400);
    }

    // Check permissions - management and reception can remove students from groups
    if (!['management', 'reception'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const context = getRequestContext(request.headers);

    try {
      // Get current relationship data
      const relationshipResult = await query(
        `SELECT sg.*, s.first_name, s.last_name, g.name as group_name
         FROM student_groups sg
         JOIN students s ON sg.student_id = s.id
         JOIN groups g ON sg.group_id = g.id
         WHERE sg.student_id = $1 AND sg.group_id = $2`,
        [id, groupId]
      );

      if (relationshipResult.rows.length === 0) {
        return createErrorResponse('Student-group relationship not found', 404);
      }

      const relationship = relationshipResult.rows[0];

      // Remove student from group (hard delete)
      await query(
        'DELETE FROM student_groups WHERE student_id = $1 AND group_id = $2',
        [id, groupId]
      );

      // Log the removal
      await logStudentOperation(
        'DELETE' as any,
        authResult.user.id,
        {
          id: relationship.student_id,
          first_name: relationship.first_name,
          last_name: relationship.last_name,
          groupRemoval: {
            groupId: relationship.group_id,
            groupName: relationship.group_name
          }
        },
        relationship,
        context
      );

      return createResponse(
        { message: 'Student removed from group successfully' },
        true,
        'Student removed from group successfully'
      );

    } catch (dbError) {
      console.error('Database error removing student from group:', dbError);
      return createErrorResponse('Failed to remove student from group', 500);
    }

  } catch (error) {
    console.error('Remove student from group error:', error);
    return createErrorResponse('Failed to remove student from group', 500);
  }
}
