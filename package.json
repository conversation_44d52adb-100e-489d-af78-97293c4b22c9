{"name": "innovative-platform", "version": "1.0.0", "description": "Innovative Centre Platform - Comprehensive CRM for English tutoring organization", "private": true, "workspaces": ["admin-server", "staff-server"], "scripts": {"dev:admin": "cd admin-server && npm run dev", "dev:staff": "cd staff-server && npm run dev", "build:admin": "cd admin-server && npm run build", "build:staff": "cd staff-server && npm run build", "start:admin": "cd admin-server && npm run start", "start:staff": "cd staff-server && npm run start", "lint": "npm run lint --workspaces", "clean": "rm -rf node_modules admin-server/node_modules staff-server/node_modules admin-server/.next staff-server/.next"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/MrFarrukhT/Innovative-Platform.git"}, "author": "Innovative Centre", "license": "MIT", "dependencies": {"node-fetch": "^3.3.2"}}