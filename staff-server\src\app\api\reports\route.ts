/**
 * Comprehensive reporting system for staff server
 * Integrates with admin server for financial data and provides operational metrics
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { createResponse, createErrorResponse, parseFilterParams } from '@/lib/utils';
import { logReportOperation, getRequestContext } from '@/lib/activity-logger';
import { adminService, safeAdminServiceCall, isAdminIntegrationEnabled } from '@/lib/admin-service';
import { UserRole } from '@/shared/types/common';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - all staff roles can view reports
    if (!['management', 'reception', 'teacher'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const filters = parseFilterParams(searchParams);
    const context = getRequestContext(request.headers);

    // Default date range (last 30 days)
    const endDate = filters.endDate || new Date().toISOString().split('T')[0];
    const startDate = filters.startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const reportType = filters.reportType || 'operational';

    try {
      let reportData: any = {};

      // Get operational metrics from staff database
      if (reportType === 'operational' || reportType === 'combined') {
        const operationalData = await getOperationalMetrics(startDate, endDate);
        reportData.operational = operationalData;
      }

      // Get financial data from admin server (if integration enabled)
      if ((reportType === 'financial' || reportType === 'combined') && isAdminIntegrationEnabled()) {
        const financialData = await safeAdminServiceCall(
          () => adminService.getKPIData({ startDate, endDate, type: 'financial' }),
          null
        );
        reportData.financial = financialData?.financial || null;
      }

      // Get consolidated data if available
      if (reportType === 'combined' && isAdminIntegrationEnabled()) {
        const consolidatedData = await safeAdminServiceCall(
          () => adminService.getConsolidatedReports({ startDate, endDate, reportType: 'combined' }),
          null
        );
        if (consolidatedData) {
          reportData.consolidated = consolidatedData;
        }
      }

      // Log report access
      await logReportOperation(
        'VIEW',
        authResult.user.id,
        `${reportType} report (${startDate} to ${endDate})`,
        context
      );

      return createResponse({
        reportType,
        dateRange: { startDate, endDate },
        data: reportData,
        generatedAt: new Date().toISOString(),
        adminIntegration: {
          enabled: isAdminIntegrationEnabled(),
          hasFinancialData: !!reportData.financial,
        }
      }, true, 'Report generated successfully');

    } catch (dbError) {
      console.error('Database error generating report:', dbError);
      return createErrorResponse('Failed to generate report', 500);
    }

  } catch (error) {
    console.error('Report generation error:', error);
    return createErrorResponse('Failed to generate report', 500);
  }
}

/**
 * Get operational metrics from staff database
 */
async function getOperationalMetrics(startDate: string, endDate: string) {
  try {
    // Student metrics
    const studentMetricsResult = await query(`
      WITH student_stats AS (
        SELECT 
          COUNT(*) as total_students,
          COUNT(*) FILTER (WHERE status = 'active') as active_students,
          COUNT(*) FILTER (WHERE status = 'inactive') as inactive_students,
          COUNT(*) FILTER (WHERE status = 'graduated') as graduated_students,
          COUNT(*) FILTER (WHERE status = 'dropped') as dropped_students,
          COUNT(*) FILTER (WHERE enrollment_date >= $1 AND enrollment_date <= $2) as new_enrollments,
          COUNT(*) FILTER (WHERE enrollment_date >= $1 AND enrollment_date <= $2 AND status = 'active') as new_active_enrollments
        FROM students
      ),
      enrollment_by_month AS (
        SELECT 
          DATE_TRUNC('month', enrollment_date) as month,
          COUNT(*) as enrollments,
          COUNT(*) FILTER (WHERE status = 'active') as active_enrollments
        FROM students 
        WHERE enrollment_date >= $1 AND enrollment_date <= $2
        GROUP BY DATE_TRUNC('month', enrollment_date)
        ORDER BY month
      )
      SELECT 
        (SELECT row_to_json(student_stats) FROM student_stats) as overview,
        (SELECT json_agg(row_to_json(enrollment_by_month)) FROM enrollment_by_month) as monthly_enrollments
    `, [startDate, endDate]);

    // Lead metrics
    const leadMetricsResult = await query(`
      WITH lead_stats AS (
        SELECT 
          COUNT(*) as total_leads,
          COUNT(*) FILTER (WHERE status = 'new') as new_leads,
          COUNT(*) FILTER (WHERE status = 'contacted') as contacted_leads,
          COUNT(*) FILTER (WHERE status = 'interested') as interested_leads,
          COUNT(*) FILTER (WHERE status = 'enrolled') as converted_leads,
          COUNT(*) FILTER (WHERE status = 'rejected') as rejected_leads,
          COUNT(*) FILTER (WHERE created_at >= $1 AND created_at <= $2) as leads_in_period,
          ROUND(
            COUNT(*) FILTER (WHERE status = 'enrolled')::decimal / 
            NULLIF(COUNT(*), 0) * 100, 2
          ) as conversion_rate
        FROM leads
      ),
      leads_by_month AS (
        SELECT 
          DATE_TRUNC('month', created_at) as month,
          COUNT(*) as total_leads,
          COUNT(*) FILTER (WHERE status = 'enrolled') as converted_leads,
          ROUND(
            COUNT(*) FILTER (WHERE status = 'enrolled')::decimal / 
            NULLIF(COUNT(*), 0) * 100, 2
          ) as monthly_conversion_rate
        FROM leads 
        WHERE created_at >= $1 AND created_at <= $2
        GROUP BY DATE_TRUNC('month', created_at)
        ORDER BY month
      ),
      leads_by_source AS (
        SELECT 
          COALESCE(source, 'Unknown') as source,
          COUNT(*) as count,
          COUNT(*) FILTER (WHERE status = 'enrolled') as converted,
          ROUND(
            COUNT(*) FILTER (WHERE status = 'enrolled')::decimal / 
            NULLIF(COUNT(*), 0) * 100, 2
          ) as conversion_rate
        FROM leads 
        WHERE created_at >= $1 AND created_at <= $2
        GROUP BY source
        ORDER BY count DESC
      )
      SELECT 
        (SELECT row_to_json(lead_stats) FROM lead_stats) as overview,
        (SELECT json_agg(row_to_json(leads_by_month)) FROM leads_by_month) as monthly_leads,
        (SELECT json_agg(row_to_json(leads_by_source)) FROM leads_by_source) as by_source
    `, [startDate, endDate]);

    // Group metrics
    const groupMetricsResult = await query(`
      WITH group_stats AS (
        SELECT 
          COUNT(*) as total_groups,
          COUNT(*) FILTER (WHERE is_active = true) as active_groups,
          AVG(max_students) as avg_capacity,
          SUM(
            (SELECT COUNT(*) FROM student_groups sg WHERE sg.group_id = g.id)
          ) as total_enrolled_students,
          ROUND(
            SUM(
              (SELECT COUNT(*) FROM student_groups sg WHERE sg.group_id = g.id)
            )::decimal / NULLIF(SUM(max_students), 0) * 100, 2
          ) as utilization_rate
        FROM groups g
        WHERE is_active = true
      ),
      groups_by_level AS (
        SELECT 
          COALESCE(level, 'No Level') as level,
          COUNT(*) as group_count,
          SUM(
            (SELECT COUNT(*) FROM student_groups sg WHERE sg.group_id = g.id)
          ) as enrolled_students,
          SUM(max_students) as total_capacity
        FROM groups g
        WHERE is_active = true
        GROUP BY level
        ORDER BY group_count DESC
      )
      SELECT 
        (SELECT row_to_json(group_stats) FROM group_stats) as overview,
        (SELECT json_agg(row_to_json(groups_by_level)) FROM groups_by_level) as by_level
    `);

    // Activity metrics
    const activityMetricsResult = await query(`
      SELECT 
        COUNT(*) as total_activities,
        COUNT(*) FILTER (WHERE action = 'CREATE') as create_actions,
        COUNT(*) FILTER (WHERE action = 'UPDATE') as update_actions,
        COUNT(*) FILTER (WHERE action = 'DELETE') as delete_actions,
        COUNT(*) FILTER (WHERE resource_type = 'STUDENT') as student_activities,
        COUNT(*) FILTER (WHERE resource_type = 'LEAD') as lead_activities,
        COUNT(*) FILTER (WHERE resource_type = 'GROUP') as group_activities,
        COUNT(DISTINCT user_id) as active_users
      FROM activity_logs 
      WHERE timestamp >= $1 AND timestamp <= $2
    `, [startDate, endDate]);

    return {
      students: studentMetricsResult.rows[0],
      leads: leadMetricsResult.rows[0],
      groups: groupMetricsResult.rows[0],
      activity: activityMetricsResult.rows[0],
      dateRange: { startDate, endDate }
    };

  } catch (error) {
    console.error('Error getting operational metrics:', error);
    throw error;
  }
}
