/**
 * Individual User API endpoint
 * Handles operations on specific users (GET, PUT, DELETE)
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, validateRequiredFields } from '@/lib/utils';
import { getUserFromRequest, hasPermission, hashPassword } from '@/lib/auth';
import { query } from '@/lib/db';
import { logUserOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/types';

interface User {
  id: string;
  email: string;
  password_hash: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

// GET /api/users/[id] - Get user by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'users', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = await params;

    try {
      // Get user by ID
      const userResult = await query<User>(
        'SELECT id, email, role, name, is_active, created_at, updated_at FROM users WHERE id = $1',
        [id]
      );

      if (userResult.rows.length === 0) {
        return createErrorResponse('User not found', 404);
      }

      const user = userResult.rows[0];

      return createResponse({
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name,
        isActive: user.is_active,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }, true, 'User retrieved successfully');

    } catch (dbError) {
      console.error('Database error getting user:', dbError);
      
      // For development, return mock data if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockUser = {
          id: id,
          email: '<EMAIL>',
          role: 'admin',
          name: 'Mock User',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        return createResponse(mockUser, true, 'User retrieved successfully (development mode)');
      }
      
      return createErrorResponse('User service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Get user error:', error);
    return createErrorResponse('Failed to get user', 500);
  }
}

// PUT /api/users/[id] - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'users', 'update')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = await params;
    const body = await request.json();
    const context = getRequestContext(request.headers);

    try {
      // Get existing user
      const existingUserResult = await query<User>(
        'SELECT id, email, role, name, is_active, created_at, updated_at FROM users WHERE id = $1',
        [id]
      );

      if (existingUserResult.rows.length === 0) {
        return createErrorResponse('User not found', 404);
      }

      const existingUser = existingUserResult.rows[0];

      // Prepare update fields
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      if (body.email !== undefined) {
        // Check if email is already taken by another user
        const emailCheckResult = await query(
          'SELECT id FROM users WHERE email = $1 AND id != $2',
          [body.email.toLowerCase(), id]
        );

        if (emailCheckResult.rows.length > 0) {
          return createErrorResponse('Email already taken by another user', 409);
        }

        updateFields.push(`email = $${paramIndex}`);
        updateValues.push(body.email.toLowerCase());
        paramIndex++;
      }

      if (body.role !== undefined) {
        const validRoles = ['admin', 'cashier', 'accountant'];
        if (!validRoles.includes(body.role)) {
          return createErrorResponse('Invalid role. Must be admin, cashier, or accountant', 400);
        }

        updateFields.push(`role = $${paramIndex}`);
        updateValues.push(body.role);
        paramIndex++;
      }

      if (body.name !== undefined) {
        updateFields.push(`name = $${paramIndex}`);
        updateValues.push(body.name);
        paramIndex++;
      }

      if (body.isActive !== undefined) {
        updateFields.push(`is_active = $${paramIndex}`);
        updateValues.push(body.isActive);
        paramIndex++;
      }

      if (body.password !== undefined) {
        const passwordHash = await hashPassword(body.password);
        updateFields.push(`password_hash = $${paramIndex}`);
        updateValues.push(passwordHash);
        paramIndex++;
      }

      if (updateFields.length === 0) {
        return createErrorResponse('No fields to update', 400);
      }

      // Add updated_at
      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

      // Add user ID for WHERE clause
      updateValues.push(id);

      // Update user
      const updateQuery = `
        UPDATE users 
        SET ${updateFields.join(', ')} 
        WHERE id = $${paramIndex}
        RETURNING id, email, role, name, is_active, created_at, updated_at
      `;

      const userResult = await query<User>(updateQuery, updateValues);
      const updatedUser = userResult.rows[0];

      // Log user update
      await logUserOperation(
        'UPDATE' as any,
        authResult.user.id,
        updatedUser,
        existingUser,
        context
      );

      return createResponse({
        id: updatedUser.id,
        email: updatedUser.email,
        role: updatedUser.role,
        name: updatedUser.name,
        isActive: updatedUser.is_active,
        createdAt: updatedUser.created_at,
        updatedAt: updatedUser.updated_at
      }, true, 'User updated successfully');

    } catch (dbError) {
      console.error('Database error updating user:', dbError);
      
      // For development, return mock response if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockUser = {
          id: id,
          email: body.email || '<EMAIL>',
          role: body.role || 'admin',
          name: body.name || 'Mock User',
          isActive: body.isActive !== undefined ? body.isActive : true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        return createResponse(mockUser, true, 'User updated successfully (development mode)');
      }
      
      return createErrorResponse('User update service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Update user error:', error);
    return createErrorResponse('Failed to update user', 500);
  }
}

// DELETE /api/users/[id] - Deactivate user (soft delete)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'users', 'delete')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = await params;
    const context = getRequestContext(request.headers);

    // Prevent self-deletion
    if (id === authResult.user.id) {
      return createErrorResponse('Cannot delete your own account', 400);
    }

    try {
      // Get existing user
      const existingUserResult = await query<User>(
        'SELECT id, email, role, name, is_active, created_at, updated_at FROM users WHERE id = $1',
        [id]
      );

      if (existingUserResult.rows.length === 0) {
        return createErrorResponse('User not found', 404);
      }

      const existingUser = existingUserResult.rows[0];

      if (!existingUser.is_active) {
        return createErrorResponse('User is already deactivated', 400);
      }

      // Deactivate user (soft delete)
      const userResult = await query<User>(
        `UPDATE users 
         SET is_active = false, updated_at = CURRENT_TIMESTAMP 
         WHERE id = $1 
         RETURNING id, email, role, name, is_active, created_at, updated_at`,
        [id]
      );

      const deactivatedUser = userResult.rows[0];

      // Log user deactivation
      await logUserOperation(
        'DELETE' as any,
        authResult.user.id,
        deactivatedUser,
        existingUser,
        context
      );

      return createResponse({
        id: deactivatedUser.id,
        email: deactivatedUser.email,
        role: deactivatedUser.role,
        name: deactivatedUser.name,
        isActive: deactivatedUser.is_active,
        createdAt: deactivatedUser.created_at,
        updatedAt: deactivatedUser.updated_at
      }, true, 'User deactivated successfully');

    } catch (dbError) {
      console.error('Database error deactivating user:', dbError);
      
      // For development, return mock response if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockUser = {
          id: id,
          email: '<EMAIL>',
          role: 'admin',
          name: 'Mock User',
          isActive: false,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        return createResponse(mockUser, true, 'User deactivated successfully (development mode)');
      }
      
      return createErrorResponse('User deactivation service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Delete user error:', error);
    return createErrorResponse('Failed to deactivate user', 500);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
